<template>
  <div class="chat-root" v-if="chat">
    <div class="title-bar">
      <input v-model="editableTitle" @blur="saveTitle" @keyup.enter="saveTitle" :disabled="isRenaming" />
      <button @click="confirmDeleteChat" :disabled="isDeleting">Delete Chat</button>
    </div>
    <div class="messages">
      <template v-for="message in displayedMessages" :key="message.node_id">
        <div class="message-item">
          <UserMessage
            v-if="message.role === 'user'"
            :message="message.content"
            :versions="getVersionInfo(message)"
            @change-version="(versionIndex) => handleVersionChange(message.node_id, versionIndex)"
            :node-id="message.node_id"
            @edit-message="promptEditUserMessage"
          />
          <LlmMessage
            v-else
            :message="message.content"
            :versions="getVersionInfo(message)"
            @change-version="(versionIndex) => handleVersionChange(message.node_id, versionIndex)"
            :node-id="message.node_id"
            @regenerate-message="handleRegenerateAssistantMessage"
          />
        </div>
      </template>
      <div v-if="isLoading" class="loading-indicator">Loading...</div>
    </div>
    <div class="input-area">
      <textarea v-model="newMessageContent" @keyup.enter="sendMessage" placeholder="Type your message..."></textarea>
      <button @click="sendMessage" :disabled="isSending">Send</button>
    </div>
  </div>
  <div v-else-if="isLoadingChat">Loading chat...</div>
  <div v-else>Select or create a chat.</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import LlmMessage from './llm-message.vue';
import UserMessage from './user-message.vue';
import { chatApiService } from '@/services/apiService';
import type { ChatSession, ChatMessageNode, MessageTurnPayload, MessageTurnResponse } from '@/types/chat';

interface DisplayMessage extends ChatMessageNode {
  // Potentially add more UI-specific fields if needed
  currentVersionIndex: number;
  versionCount: number;
}

// Props if this component takes a chatId, or manage active chatId internally
const props = defineProps<{
  activeChatId: string | null;
}>();

const chat = ref<ChatSession | null>(null);
const isLoading = ref(false); // For message sending/regen
const isLoadingChat = ref(false); // For loading initial chat data
const isSending = ref(false);
const isRenaming = ref(false);
const isDeleting = ref(false);

const newMessageContent = ref('');
const editableTitle = ref('');

// Helper to find a node by ID from the local chat.message_nodes
const findNodeById = (nodeId: string | null): ChatMessageNode | undefined => {
  if (!nodeId || !chat.value) return undefined;
  return chat.value.message_nodes.find(node => node.node_id === nodeId);
};

// This computed property generates the linear list of messages to display based on the active path
const displayedMessages = computed<DisplayMessage[]>(() => {
  if (!chat.value || !chat.value.entry_node_id) return [];

  const result: DisplayMessage[] = [];
  let currentNode = findNodeById(chat.value.entry_node_id);

  while (currentNode) {
    const activeIndex = currentNode.active_next_node_id
      ? currentNode.next_node_ids.findIndex(id => id === currentNode.active_next_node_id)
      : -1; // -1 if no active next node or it's a leaf

    result.push({
      ...currentNode,
      currentVersionIndex: activeIndex, // 0-indexed
      versionCount: currentNode.next_node_ids?.length || 0,
    });

    if (!currentNode.active_next_node_id) break; // End of active path
    currentNode = findNodeById(currentNode.active_next_node_id);
  }
  return result;
});

function getVersionInfo(node: ChatMessageNode) {
  const versionCount = node.next_node_ids?.length || 0;
  const currentIndex = node.active_next_node_id
    ? node.next_node_ids.findIndex(id => id === node.active_next_node_id)
    : -1; // Current version (0-indexed)
  return { current: currentIndex + 1, total: versionCount }; // 1-indexed for UI
}

watch(() => props.activeChatId, async (newId) => {
  if (newId) {
    await loadChatDetails(newId);
  } else {
    chat.value = null;
    editableTitle.value = '';
  }
}, { immediate: true });

watch(chat, (newChat) => {
  if (newChat) {
    editableTitle.value = newChat.title || '';
  }
}, { deep: true });

async function loadChatDetails(chatId: string) {
  isLoadingChat.value = true;
  try {
    const chatData = await chatApiService.getChatDetails(chatId);
    chat.value = chatData;
  } catch (error) {
    console.error("Failed to load chat details:", error);
    // Show error to user
  } finally {
    isLoadingChat.value = false;
  }
}

async function saveTitle() {
  if (!chat.value || chat.value.title === editableTitle.value.trim()) return;
  isRenaming.value = true;
  try {
    await chatApiService.renameChat(chat.value.id, editableTitle.value.trim());
    if (chat.value) { // Check again, might have changed
        chat.value.title = editableTitle.value.trim(); // Optimistic update or use response
    }
  } catch (error) {
    console.error("Failed to rename chat:", error);
    editableTitle.value = chat.value?.title || ''; // Revert on error
  } finally {
    isRenaming.value = false;
  }
}

async function confirmDeleteChat() {
    if(!chat.value) return;
    if(confirm(`Are you sure you want to delete "${chat.value.title}"? This cannot be undone.`)){
        isDeleting.value = true;
        try {
            await chatApiService.deleteChat(chat.value.id);
            chat.value = null;
            alert("Chat deleted successfully.");
        } catch (error) {
            console.error("Failed to delete chat:", error);
            alert("Error deleting chat.");
        } finally {
            isDeleting.value = false;
        }
    }
}

// Centralized function to update local chat state from API response
function updateChatStateFromResponse(response: MessageTurnResponse) {
  if (!chat.value) return;

  const { new_user_node, new_assistant_node, updated_origin_node_links, chat_session_meta_update } = response;

  // 1. Add new nodes
  if (new_user_node) {
    const existingUserNodeIndex = chat.value.message_nodes.findIndex(n => n.node_id === new_user_node.node_id);
    if (existingUserNodeIndex > -1) chat.value.message_nodes[existingUserNodeIndex] = new_user_node;
    else chat.value.message_nodes.push(new_user_node);
  }
  if (new_assistant_node) {
     const existingAssistantNodeIndex = chat.value.message_nodes.findIndex(n => n.node_id === new_assistant_node.node_id);
    if (existingAssistantNodeIndex > -1) chat.value.message_nodes[existingAssistantNodeIndex] = new_assistant_node;
    else chat.value.message_nodes.push(new_assistant_node);
  }

  // 2. Update links of the origin node (if any was affected and returned)
  if (updated_origin_node_links) {
    const originIndex = chat.value.message_nodes.findIndex(n => n.node_id === updated_origin_node_links.node_id);
    if (originIndex > -1) {
      chat.value.message_nodes[originIndex] = {
          ...chat.value.message_nodes[originIndex], // Keep other properties
          ...updated_origin_node_links // Override with new link info
      };
    } else {
        console.warn("Updated origin_node_links for a node not found locally:", updated_origin_node_links);
    }
  }

  // 3. Update chat session metadata
  chat.value.last_update_timestamp = chat_session_meta_update.updated_at;
  if (chat_session_meta_update.entry_node_id && !chat.value.entry_node_id) {
    chat.value.entry_node_id = chat_session_meta_update.entry_node_id;
  } else if (chat_session_meta_update.entry_node_id && chat.value.entry_node_id !== chat_session_meta_update.entry_node_id) {
    chat.value.entry_node_id = chat_session_meta_update.entry_node_id;
  }
}

async function sendMessage() {
  if (!newMessageContent.value.trim() || !chat.value) return;
  isSending.value = true;

  // Determine origin_node_id: last node in the current displayedMessages (active path)
  // If displayedMessages is empty, it's the first message, origin_node_id is null.
  const lastDisplayedNode = displayedMessages.value.length > 0
    ? displayedMessages.value[displayedMessages.value.length - 1]
    : null;

  const payload: MessageTurnPayload = {
    origin_node_id: lastDisplayedNode ? lastDisplayedNode.node_id : null,
    action_type: 'new_message',
    user_content: newMessageContent.value.trim(),
    metadata: { edit_source: "web_client_initial_prompt" } // Example metadata
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
    newMessageContent.value = ''; // Clear input
  } catch (error) {
    console.error("Failed to send message:", error);
    // Show error to user
  } finally {
    isSending.value = false;
  }
}

// Called when user wants to edit their own message
async function promptEditUserMessage(targetUserNodeId: string) {
  if (!chat.value) return;
  const targetNode = findNodeById(targetUserNodeId);
  if (!targetNode || targetNode.role !== 'user') {
    console.error("Cannot edit: Node not found or not a user node.");
    return;
  }

  const newContent = prompt("Enter new content for your message:", targetNode.content);
  if (newContent === null || newContent.trim() === "" || newContent.trim() === targetNode.content) {
    return; // User cancelled, entered empty, or no change
  }

  // Find the predecessor of targetUserNodeId to set as origin_node_id
  let predecessorNodeId: string | null = null;
  const path = displayedMessages.value; // Current active path
  const targetIndexInPath = path.findIndex(m => m.node_id === targetUserNodeId);
  if (targetIndexInPath > 0) {
      predecessorNodeId = path[targetIndexInPath - 1].node_id;
  } else if (targetIndexInPath === 0) { // targetNode is the entry_node_id
      predecessorNodeId = null; // For backend to know it's branching from root
  } else {
      console.error("Could not find predecessor for edit. Node may not be in active path.");
      return;
  }

  isLoading.value = true; // General loading state
  const payload: MessageTurnPayload = {
    origin_node_id: predecessorNodeId,
    action_type: 'edit_user_message',
    user_content: newContent.trim(),
    metadata: { edit_source: "web_client_edit" }
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
  } catch (error) {
    console.error("Failed to edit message:", error);
    alert("Error editing message.");
  } finally {
    isLoading.value = false;
  }
}

// Called when user wants to regenerate an assistant's response
async function handleRegenerateAssistantMessage(targetAssistantNodeId: string) {
  if (!chat.value) return;
  const assistantNode = findNodeById(targetAssistantNodeId);
  if (!assistantNode || assistantNode.role !== 'assistant') {
    console.error("Cannot regenerate: Node not found or not an assistant node.");
    return;
  }

  // For 'regenerate_assistant_message', origin_node_id is the USER message
  // that PRECEDES this assistantNode.
  let userPredecessorNodeId: string | null = null;
  const path = displayedMessages.value;
  const assistantIndexInPath = path.findIndex(m => m.node_id === targetAssistantNodeId);
  if (assistantIndexInPath > 0 && path[assistantIndexInPath - 1].role === 'user') {
    userPredecessorNodeId = path[assistantIndexInPath - 1].node_id;
  } else {
    console.error("Failed to find user predecessor for regeneration.");
    alert("Error: Could not determine context for regeneration.");
    return;
  }

  if (!userPredecessorNodeId) {
      console.error("Could not find the user message preceding this assistant message for regeneration.");
      return;
  }

  isLoading.value = true;
  const payload: MessageTurnPayload = {
    origin_node_id: userPredecessorNodeId, // This is the user message node.
    action_type: 'regenerate_assistant_message',
    // user_content is not needed for regenerate
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
  } catch (error) {
    console.error("Failed to regenerate assistant message:", error);
    alert("Error regenerating response.");
  } finally {
    isLoading.value = false;
  }
}

// This function is called when user clicks version switcher on a message.
async function handleVersionChange(nodeIdToUpdateLinks: string, newVersionIndex: number) { // newVersionIndex is 1-based
  if (!chat.value) return;
  const nodeToUpdate = findNodeById(nodeIdToUpdateLinks);

  if (!nodeToUpdate || !nodeToUpdate.next_node_ids || nodeToUpdate.next_node_ids.length === 0) {
    console.warn("handleVersionChange called on a node with no versions or not found:", nodeIdToUpdateLinks);
    return;
  }

  const newActiveNextNodeId = nodeToUpdate.next_node_ids[newVersionIndex - 1]; // 0-indexed for array
  if (!newActiveNextNodeId || nodeToUpdate.active_next_node_id === newActiveNextNodeId) {
    return; // No change or invalid index
  }

  isLoading.value = true;
  try {
    await chatApiService.setActiveBranch(chat.value.id, nodeIdToUpdateLinks, newActiveNextNodeId);
    // Optimistic update:
    nodeToUpdate.active_next_node_id = newActiveNextNodeId;
    // The displayedMessages computed property will auto-update the view.
  } catch (error) {
    console.error("Failed to set active branch:", error);
    alert("Error switching version.");
  } finally {
    isLoading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.chat-root {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.title-bar {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ccc;
  input {
    flex-grow: 1;
    font-size: 1.2em;
    border: none;
    padding: 5px;
  }
  input:disabled {
    background-color: #f0f0f0;
  }
  button {
    margin-left: 10px;
  }
}

.messages {
  display: flex;
  flex-direction: column;
  margin: auto;
  width: 100%;
  max-width: 780px;
  margin-top: 10px;
}

.loading-indicator, .input-area {
  padding: 10px;
  text-align: center;
}

.input-area {
  display: flex;
  textarea {
    flex-grow: 1;
    min-height: 50px;
    margin-right: 10px;
  }
}

.message-item {
  margin-bottom: 10px; /* Add some space between messages */
}
</style>