# **聊天应用 API 文档**

**API 前缀**: /api/chats

**认证**: 所有端点均需要用户登录认证 (通过 flask\_login 的 @login\_required 实现)。前端需要确保在请求头中包含有效的会话 cookie 或认证 token。

## **1\. 会话管理 (Conversations)**

### **1.1 创建新的聊天会话**

* **POST** /api/chats  
* **描述**: 创建一个新的聊天会话。  
* **认证**: 需要  
* **请求体** (JSON):  
  {  
      "title": "可选的会话标题"  
  }

  * title (string, 可选): 新会话的标题。如果未提供，默认为 "New Chat"。  
* **成功响应** (201):  
  {  
      "id": "string", // 会话 ID (注意：这里是字符串类型)  
      "user\_id": "integer",  
      "title": "string",  
      "created\_at": "string (ISO 8601)",  
      "updated\_at": "string (ISO 8601)",  
      "entry\_node\_id": null, // 初始为空  
      "message\_nodes": \[\] // 初始为空  
  }

* **错误响应**:  
  * 401 Unauthorized: 用户未登录。

### **1.2 获取用户的所有聊天会话列表**

* **GET** /api/chats  
* **描述**: 获取当前已登录用户的所有聊天会话的元数据列表，按更新时间降序排列。  
* **认证**: 需要  
* **请求体**: 无  
* **成功响应** (200):  
  \[  
      {  
          "id": "string", // 会话 ID (注意：这里是字符串类型)  
          "user\_id": "integer",  
          "title": "string",  
          "created\_at": "string (ISO 8601)",  
          "updated\_at": "string (ISO 8601)",  
          "entry\_node\_id": "string | null" // 入口消息节点的 ID  
      },  
      // ... 更多会话元数据  
  \]

  * 注意：id 在模型中是 integer，但在 to\_dict\_metadata (以及 to\_dict\_full 的 id 字段) 中被转换为字符串返回给前端。  
* **错误响应**:  
  * 401 Unauthorized: 用户未登录。

### **1.3 获取指定聊天会话的完整详情**

* **GET** /api/chats/\<string:chat\_id\_str\>  
* **描述**: 获取指定 ID 的聊天会话的完整详情，包括所有消息节点。  
* **认证**: 需要  
* **路径参数**:  
  * chat\_id\_str (string): 要获取详情的会话 ID。  
* **请求体**: 无  
* **成功响应** (200):  
  {  
      "id": "string", // 会话 ID  
      "user\_id": "integer",  
      "title": "string",  
      "created\_at": "string (ISO 8601)",  
      "updated\_at": "string (ISO 8601)",  
      "entry\_node\_id": "string | null", // 聊天入口消息节点的ID  
      "message\_nodes": \[ // 按时间戳升序排列（基于 build\_llm\_history 的简化逻辑推断，但模型定义中未明确排序）  
          {  
              "node\_id": "string", // 消息节点 UUID  
              "conversation\_id": "integer", // 所属会话 ID (数据库中的整数 ID)  
              "role": "user | assistant",  
              "content": "string",  
              "timestamp": "string (ISO 8601)",  
              "parent\_node\_id": "string | null", // （代码中未使用，但模型中可能存在）  
              "next\_node\_ids": \["string", ...\], // 后续可能的消息节点 ID 列表  
              "active\_next\_node\_id": "string | null", // 当前激活的下一条消息节点 ID  
              "message\_metadata": {} // 额外元数据  
          },  
          // ... 更多消息节点  
      \]  
  }

* **错误响应**:  
  * 400 Bad Request: 如果 chat\_id\_str 不是有效的整数格式。  
    { "message": "Invalid chat\_id format" }

  * 401 Unauthorized: 用户未登录。  
  * 404 Not Found: 如果会话不存在或不属于当前用户。

### **1.4 删除指定的聊天会话**

* **DELETE** /api/chats/\<string:chat\_id\_str\>  
* **描述**: 删除指定 ID 的聊天会话及其所有相关的消息节点。  
* **认证**: 需要  
* **路径参数**:  
  * chat\_id\_str (string): 要删除的会话 ID。  
* **请求体**: 无  
* **成功响应** (200):  
  { "message": "Conversation deleted" }

* **错误响应**:  
  * 400 Bad Request: 如果 chat\_id\_str 不是有效的整数格式。  
    { "message": "Invalid chat\_id format" }

  * 401 Unauthorized: 用户未登录。  
  * 404 Not Found: 如果会话不存在或不属于当前用户。  
  * 400 Bad Request (可选逻辑，代码中已注释掉):  
    // { "message": "Cannot delete the last conversation." }

### **1.5 重命名指定的聊天会话**

* **PUT** /api/chats/\<string:chat\_id\_str\>/rename  
* **描述**: 修改指定聊天会话的标题。  
* **认证**: 需要  
* **路径参数**:  
  * chat\_id\_str (string): 要重命名的会话 ID。  
* **请求体** (JSON):  
  {  
      "title": "新的会话标题"  
  }

  * title (string, 必填): 新的会话标题，长度必须在 1 到 150 个字符之间。  
* **成功响应** (200):  
  {  
      "message": "Conversation title updated",  
      "id": "string", // 会话 ID  
      "new\_title": "string" // 更新后的标题  
  }

* **错误响应**:  
  * 400 Bad Request: 如果 chat\_id\_str 不是有效的整数格式。  
    { "message": "Invalid chat\_id format" }

  * 400 Bad Request: 如果 title 无效 (为空、仅空格或长度超出限制)。  
    { "message": "Valid title is required (1-150 chars)" }

  * 401 Unauthorized: 用户未登录。  
  * 404 Not Found: 如果会话不存在或不属于当前用户。

## **2\. 消息交互 (Message Turns)**

### **2.1 发送/处理消息轮次**

* **POST** /api/chats/\<string:chat\_id\_str\>/message\_turns  
* **描述**: 在指定的聊天会话中处理一个消息轮次。这包括发送新消息、编辑用户消息或重新生成助手消息。服务器将处理用户输入，调用 LLM 服务，然后返回新的用户消息节点（如果适用）、新的助手消息节点以及可能更新的原始节点链接。  
* **认证**: 需要  
* **路径参数**:  
  * chat\_id\_str (string): 消息所属的会话 ID。  
* **请求体** (JSON):  
  {  
      "origin\_node\_id": "string | null",  
      "action\_type": "new\_message | edit\_user\_message | regenerate\_assistant\_message",  
      "user\_content": "string | null",  
      "metadata": {} // 可选，用于用户消息的元数据  
  }

  * origin\_node\_id (string, 可选):  
    * 对于 new\_message: 如果提供，表示新消息是 origin\_node\_id 的一个分支；如果为 null，表示这是会话中的第一条消息，或者是一个新的无父级分支的开始（当前实现主要针对空会话的第一条消息）。  
    * 对于 edit\_user\_message: **必须**提供，指向被编辑的用户消息的**前一个**消息节点。LLM 将基于编辑后的用户消息内容重新生成回应。  
    * 对于 regenerate\_assistant\_message: **必须**提供，指向需要重新生成回应的**用户消息节点**。  
  * action\_type (string, 必填):  
    * new\_message: 用户发送一条新消息。  
    * edit\_user\_message: 用户编辑了自己之前发送的一条消息。  
    * regenerate\_assistant\_message: 用户请求重新生成 origin\_node\_id (用户消息) 对应的助手回应。  
  * user\_content (string, 条件必填):  
    * 对于 new\_message 和 edit\_user\_message 是**必填**的。  
    * 对于 regenerate\_assistant\_message 则不需要。  
  * metadata (object, 可选): 附加到新创建的用户消息节点的元数据。  
* **成功响应** (201):  
  {  
      "new\_user\_node": { /\* 消息节点对象, 见 1.3 \*/ } | null, // 如果是 'new\_message' 或 'edit\_user\_message'，则为新创建/更新的用户节点数据；'regenerate\_assistant\_message' 时为 null  
      "new\_assistant\_node": { /\* 消息节点对象, 见 1.3 \*/ }, // 新创建的助手节点数据  
      "updated\_origin\_node\_links": { /\* 消息节点对象, 见 1.3 \*/ } | null, // 如果 origin\_node\_id 被提供了且其链接被更新，则返回其更新后的数据；如果新消息是会话中的第一条，则为 null  
      "chat\_session\_meta\_update": {  
          "id": "string", // 会话 ID  
          "updated\_at": "string (ISO 8601Z)", // 会话更新时间  
          "entry\_node\_id": "string | null" // 可能会更新的会话入口节点 ID  
      }  
  }

  * new\_user\_node: 包含新创建或被编辑的用户消息节点的完整信息。如果 action\_type 是 regenerate\_assistant\_message，此字段为 null。  
  * new\_assistant\_node: 包含新生成的助手消息节点的完整信息。  
  * updated\_origin\_node\_links: 如果 origin\_node\_id 被提供（例如，在创建分支、编辑或重新生成时），此字段包含 origin\_node\_id 指向的那个节点更新后的链接信息 (next\_node\_ids, active\_next\_node\_id)。如果 action\_type 是 new\_message 且 origin\_node\_id 为 null（即会话中的第一条消息），此字段为 null。  
* **错误响应**:  
  * 400 Bad Request:  
    * { "message": "Invalid chat\_id format" }  
    * { "message": "Invalid action\_type" }  
    * { "message": "user\_content is required for this action\_type" } (当 action\_type 是 new\_message 或 edit\_user\_message 但 user\_content 未提供时)  
    * { "message": "For regenerate\_assistant\_message, origin\_node\_id must point to a user message." } (当 action\_type 是 regenerate\_assistant\_message 但 origin\_node\_id 未提供或指向非用户消息时)  
  * 401 Unauthorized: 用户未登录。  
  * 404 Not Found:  
    * 会话不存在或不属于当前用户。  
    * { "message": "Origin node \<origin\_node\_id\> not found in this chat." } (如果提供的 origin\_node\_id 在会话中找不到)  
  * 500 Internal Server Error:  
    * { "message": "Internal error: user context for LLM not established." }  
    * { "message": "Error getting response from LLM", "details": "\<error\_details\_from\_llm\>" } (LLM 服务调用失败时)

## **3\. 消息节点操作 (Message Nodes)**

### **3.1 设置节点激活的下一分支**

* **PUT** /api/chats/\<string:chat\_id\_str\>/nodes/\<string:node\_id\>/set\_active\_branch  
* **描述**: 当一个消息节点 (node\_id) 有多个可能的后续消息时 (即 next\_node\_ids 列表有多个元素)，此接口用于设置哪一个后续消息是当前激活的路径/分支。  
* **认证**: 需要  
* **路径参数**:  
  * chat\_id\_str (string): 会话 ID。  
  * node\_id (string): 需要更新其 active\_next\_node\_id 的消息节点的 ID。  
* **请求体** (JSON):  
  {  
      "active\_next\_node\_id": "string"  
  }

  * active\_next\_node\_id (string, 必填): 要设置为激活状态的后续消息节点的 ID。此 ID **必须**存在于 node\_id 指向的节点的 next\_node\_ids 列表中。  
* **成功响应** (200):  
  {  
      "message": "Active branch switched",  
      "updated\_node\_id": "string", // 被更新的节点 ID  
      "new\_active\_next\_node\_id": "string" // 新设置的激活的下一节点 ID  
  }

* **错误响应**:  
  * 400 Bad Request:  
    * { "message": "Invalid chat\_id format" }  
    * { "message": "active\_next\_node\_id is required in payload." }  
    * { "message": "\<active\_next\_node\_id\_from\_request\> is not a valid next node for \<node\_id\>." } (如果提供的 active\_next\_node\_id 不在目标节点的 next\_node\_ids 列表中)  
  * 401 Unauthorized: 用户未登录。  
  * 404 Not Found:  
    * 会话不存在或不属于当前用户。  
    * { "message": "Node \<node\_id\> not found in this chat." } (如果 node\_id 在会话中找不到)

## **辅助函数 (内部逻辑，前端无需直接调用)**

### **build\_llm\_history(chat\_id, current\_turn\_user\_node)**

* **描述**: 此函数在后端用于为 LLM 构建消息历史。它会从会话的 entry\_node\_id 开始（如果存在），沿着 active\_next\_node\_id 链接遍历，直到（并包括）current\_turn\_user\_node。它还会预置一个系统提示。  
* **注意**:  
  * 当前代码中的 build\_llm\_history 实现有一个简化处理：它通过时间戳排序获取节点，这可能无法完全准确地处理复杂的分支情况。前端应意识到，LLM 的上下文是基于后端如何构建这个历史记录的。  
  * 系统提示内容从 SystemSetting 表中获取，键为 system\_prompt。如果未设置，默认为 "You are a helpful assistant."

## **数据模型要点 (供前端参考)**

### **Conversation**

* id (integer, PKey): 会话的唯一标识。**注意**: API 响应中通常作为字符串返回。  
* user\_id (integer, FKey): 所属用户的 ID。  
* title (string): 会话标题。  
* created\_at (datetime): 创建时间。  
* updated\_at (datetime): 最后更新时间。  
* entry\_node\_id (string/UUID, nullable): 聊天流的起始消息节点 ID。

### **ChatMessageNode**

* node\_id (string/UUID, PKey): 消息节点的唯一标识。  
* conversation\_id (integer, FKey): 所属会话的 ID。  
* role (string): 消息发送者角色，'user' 或 'assistant'。  
* content (text): 消息内容。  
* timestamp (datetime): 消息创建时间。  
* parent\_node\_id (string/UUID, nullable): （模型中可能存在，但当前 API 逻辑主要通过 next\_node\_ids 和 active\_next\_node\_id 管理关系）。  
* next\_node\_ids (JSON, nullable): 存储一个或多个后续消息节点 node\_id 的列表。表示此消息之后可能的分支。  
* active\_next\_node\_id (string/UUID, nullable): 如果有多个 next\_node\_ids，此字段指定当前激活的“下一条”消息的 node\_id，从而定义了主对话流。  
* message\_metadata (JSON, nullable): 存储与消息相关的额外元数据 (例如，LLM 模型信息，用户上传的文件引用等)。

### **SystemSetting**

* key (string, PKey): 设置项的键名 (例如, "system\_prompt")。  
* value (text): 设置项的值。

**重要注意事项**:

* **ID 类型**: Conversation.id 在数据库中是整数，但在 API 响应中，为了与 ChatMessageNode.node\_id (UUID 字符串) 保持一致性或特定前端需求，它被显式转换为字符串类型 (chat\_id\_str)。前端在发送请求时，路径参数 chat\_id\_str 应为字符串，后端会将其转换回整数进行数据库查询。  
* **时间戳**: 所有时间戳均以 ISO 8601 格式字符串返回 (例如, "2023-10-27T10:30:00Z" 或 "2023-10-27T10:30:00.123456Z")。  
* **分支与历史**: build\_llm\_history 的当前实现较为简化，主要依赖时间戳。在复杂分支场景下，前端应关注 active\_next\_node\_id 来理解当前对话的主线。  
* **错误处理**: API 会返回标准的 HTTP 状态码。对于 4xx 和 5xx 错误，响应体通常包含一个带有 message 键的 JSON 对象，提供错误详情。