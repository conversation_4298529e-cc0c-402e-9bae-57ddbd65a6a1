<script setup>
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import ChatInputBox from "@/components/chat-initial/ChatInputBox.vue";
import IconLogoDeepseek from "./components/icons/IconLogoDeepseek.vue";
import Sidebar from "@/components/sidebar/sidebar.vue";
import Chat from "./components/chat/chat.vue";
import { useChatStore } from '@/store/chat';

const route = useRoute();
const chatStore = useChatStore();

// 计算当前是否在对话页面
const isInChatSession = computed(() => {
  return route.name === 'chat-session' && route.params.id;
});

// 获取当前对话ID
const currentChatId = computed(() => {
  return isInChatSession.value ? String(route.params.id) : null;
});

// 监听路由变化，更新store中的当前对话ID
watch(currentChatId, (newChatId) => {
  chatStore.setCurrentChatId(newChatId);
}, { immediate: true });
</script>

<template>
  <div id="root">
    <Sidebar />

    <!-- 初始聊天界面 - 当没有选中对话时显示 -->
    <div v-if="!isInChatSession" class="chat-input-container">
      <div class="chat-header">
        <div class="logo-wrapper">
          <IconLogoDeepseek />
        </div>
        我是 DeepSeek，很高兴见到你！
      </div>
      <div class="chat-content">
        我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~
      </div>
      <ChatInputBox style="width: 100%;" />
    </div>

    <!-- 对话界面 - 当选中对话时显示 -->
    <div v-else class="chat-container">
      <Chat :activeChatId="currentChatId" />
    </div>
  </div>
</template>

<style scoped>
#root {
  display: flex;
  height: 100%;
  width: 100%;

}

.chat-input-container, .chat-container {
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  width: 100%;
  font-size: 14px;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;
}

.chat-input-container {
   max-width: 800px;
}

.chat-header {
  align-items: center;
  justify-content: center;
  gap: 14px;
  display: flex;
  font-size: 24px;
  font-weight: 500;
  flex-direction: row;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}

.logo-wrapper {
  display: flex;
  line-height: 0px;
  left: auto;
  position: static;
  font-size: 60px;
  width: 60px;
  height: 60px;
}

.logo-icon {
  height: 60px;
  width: 60px;
}

.chat-content {
  color: rgb(64, 64, 64);
  font-size: 14px;
  margin: 8px 0px 20px;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}



header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
