<template>
  <div class="container" @mouseover="handleMouse(true)" @mouseout="handleMouse(false)">
    <div class="user-message">
      {{ message }}
    </div>
    <div class="message-actions-toolbar" :style="{ opacity: showShowToolbar ? '0.7' : '0' }">
      <div class="action-button copy-button" @click="copyMessage">
        <div class="action-icon-wrapper">
          <IconCopy />
        </div>
      </div>
      <div class="action-button edit-button" @click="editMessage">
        <div class="action-icon-wrapper">
          <IconEdit />
        </div>
      </div>
      <VersionController v-if="showVersionController" v-bind="props.versions" @change-version="handleVersionChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { PropType } from 'vue';
import IconCopy from '../icons/IconCopy.vue';
import IconEdit from '../icons/IconEdit.vue';
import VersionController from './version-controller.vue';

interface VersionControllerProps {
  current: number;
  total: number;
  loading?: boolean;
}

const props = defineProps({
  message: String,
  alwaysShowToolbar: Boolean,
  nodeId: String, // Add nodeId prop
  versions: {
    type: Object as PropType<VersionControllerProps>,
    required: false
  }
});

const showVersionController = computed(() => {
  return props.versions && props.versions.total > 1;
});

const showToolbar = ref(false);
let showTimer: number | undefined = undefined;
let hideTimer: number | undefined = undefined;

const showShowToolbar = computed(() => showToolbar.value || props.alwaysShowToolbar);

const copyMessage = () => {
  console.log('Copy action triggered');
  // 示例：navigator.clipboard.writeText(props.message || '');
  // 或 emit 事件：emit('copy', props.message);
};

const editMessage = () => {
  console.log('Edit action triggered');
  if (props.nodeId) {
    emit('edit-message', props.nodeId);
  }
};

let debounceTimer: number | undefined = undefined;

const emit = defineEmits(['change-version', 'edit-message']);

const handleVersionChange = (newVersion: number) => {
  emit('change-version', newVersion);
};

const handleMouse = (show: boolean) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = window.setTimeout(() => {
    showToolbar.value = show;
  }, 50);
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  display: flex;
  flex-direction: column;  // 调整为列布局，以便工具栏在消息下方
  align-items: flex-end;   // 保持右对齐
}

.user-message {
  display: inline-block;
  background-color: #eff6ff;
  padding: 12px 18px;
  border-radius: 10px;
  position: relative;
}

.message-actions-toolbar {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.7;
  transition: opacity 0.1s ease-in-out;
  margin-top: 5px;  // 添加一些间距，使其在消息下方
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgb(73, 73, 73);
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease, color 0.2s ease;
  padding: 2px;

  &:hover {
    color: #404040;
    background-color: #f5f5f5;
  }
}

.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  :deep(svg) {
    width: 18px;
    height: 18px;
    fill: currentColor;
  }
}
</style>
